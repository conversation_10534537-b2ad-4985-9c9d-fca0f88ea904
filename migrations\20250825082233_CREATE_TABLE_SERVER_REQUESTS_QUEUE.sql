-- migrate:up
CREATE TABLE server_request_queue (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    device_status SMALLINT(5) DEFAULT 0,
    enpoint <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL,
    action VARCHAR(255) DEFAULT NULL,
    error TINYINT(4) DEFAULT 0,
    payload LONGTEXT DEFAULT NULL,
    sent_attemps SMALLINT(5) DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- migrate:down
DROP TABLE server_request_queue;
